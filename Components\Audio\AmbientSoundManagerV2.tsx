import React, { useEffect, useRef, useCallback } from 'react';
import { gsap } from 'gsap';
// 🔧 CISCO: SUPPRESSION - Plus d'interaction avec le temporisateur de journée
// import { useDayCycleOptional } from '../Context/DayCycleContext';

// 🔧 CISCO: Système audio INDÉPENDANT - Plus d'interaction avec le temporisateur
interface AmbientSoundManagerV2Props {
  skyMode: string; // 🔧 CISCO: OBLIGATOIRE - Plus de contexte automatique
  enabled?: boolean;
  volume?: number; // 0 to 1
}

// 🎵 Configuration des sons par mode - correspondance exacte avec les dossiers
// 🎬 CISCO: SYNCHRONISATION CINÉMA - Phases françaises pour correspondre au système cinématographique
const SOUND_CONFIG: Record<string, {
  sounds: string[]; // Plusieurs sons possibles par mode
  volume: number;
  folder: string;
  fadeInDuration?: number;
  fadeOutDuration?: number;
  repeatDelay?: number; // 🔧 CISCO: D<PERSON><PERSON> de répétition pour certains sons courts
}> = {
    // 🌙 Nuit profonde - 3 sons disponibles avec temporisation hibou
    nuit: {
      sounds: ['hibou-molkom.mp3', 'night-atmosphere-with-crickets-374652.mp3', 'sounds-crickets-nuit_profonde.mp3'],
      volume: 0.6,
      folder: 'nuit-profonde',
      fadeInDuration: 12000, // 🔧 CISCO: Fondu d'entrée plus long (12s) pour accompagner la transition visuelle
      fadeOutDuration: 5000,
      repeatDelay: 90000 // 🔧 CISCO: Temporisation 1.5 minute (90s) pour hibou-molkom.mp3
    },

    // 🌅 Aube - 1 son disponible (CISCO: Volume réduit pour merle trop fort)
    aube: {
      sounds: ['village_morning_birds_roosters.mp3'],
      volume: 0.3, // 🔧 CISCO: Réduit de 0.5 à 0.3 car merle trop fort
      folder: 'aube',
      fadeInDuration: 5000,
      fadeOutDuration: 5000
    },

    // ☀️ Midi - 2 sons disponibles
    midi: {
      sounds: ['forest_cicada.mp3', 'campagne-birds.mp3'],
      volume: 0.5,
      folder: 'midi',
      fadeInDuration: 4000,
      fadeOutDuration: 4000
    },

    // 🌆 Coucher du soleil - 2 sons disponibles
    coucher: {
      sounds: ['bird-chirp.mp3', 'grillon-drome.mp3'],
      volume: 0.4,
      folder: 'coucher-soleil',
      fadeInDuration: 5000,
      fadeOutDuration: 5000
    },

    // 🔧 CISCO: SUPPRESSION DUPLICATION - Seules les phases françaises sont conservées
    // Les phases anglaises (night, dawn, midday, sunset) sont supprimées pour éviter
    // la désynchronisation audio (merle qui jouait dans tous les modes)
};

// 🎵 Fonction pour construire l'URL du son
const getSoundUrl = (soundFile: string, folder: string): string => {
  return `/sounds/${folder}/${soundFile}`;
};

// 🔧 CISCO: Fonction getRandomSound supprimée - on joue TOUS les sons maintenant

const AmbientSoundManagerV2: React.FC<AmbientSoundManagerV2Props> = ({
  skyMode,
  enabled = true,
  volume = 1.0
}) => {
  // 🔧 CISCO: SYSTÈME INDÉPENDANT - Plus de contexte DayCycle
  // L'audio fonctionne maintenant uniquement via les props skyMode

  // 🔧 CISCO: Mode actuel déterminé uniquement par les props
  const currentSkyMode = skyMode;

  // 🔧 CISCO: État pour gérer l'initialisation audio (permissions navigateur)
  const audioContextInitialized = useRef(false);

  // 🔧 CISCO: Changement pour gérer PLUSIEURS fichiers audio simultanément
  const audioRefs = useRef<HTMLAudioElement[]>([]);
  const fadeTweens = useRef<gsap.core.Tween[]>([]);
  const currentSoundKey = useRef<string | null>(null);
  const currentSoundFiles = useRef<string[]>([]);
  const repeatTimeouts = useRef<NodeJS.Timeout[]>([]); // 🔧 CISCO: Gestion des timeouts de répétition
  const lastModeChangeTime = useRef<number>(0); // 🔧 CISCO: Protection contre les changements trop rapides

  // 🔧 CISCO: Fonction pour initialiser le contexte audio après interaction utilisateur
  const initializeAudioContext = useCallback(async () => {
    if (audioContextInitialized.current) return true;

    try {
      // Créer un contexte audio temporaire pour tester les permissions
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      if (audioContext.state === 'suspended') {
        await audioContext.resume();
      }

      // Fermer le contexte de test
      await audioContext.close();

      audioContextInitialized.current = true;
      console.log('✅ Contexte audio initialisé avec succès');
      return true;
    } catch (error) {
      console.error('❌ Erreur initialisation contexte audio:', error);
      return false;
    }
  }, []);

  // 🔧 CISCO: Fonction pour jouer un audio avec initialisation du contexte
  const playAudioWithContext = useCallback(async (audio: HTMLAudioElement) => {
    const contextReady = await initializeAudioContext();
    if (!contextReady) {
      console.warn('⚠️ Contexte audio non initialisé, tentative de lecture quand même');
    }
    return audio.play();
  }, [initializeAudioContext]);

  // 🔧 CISCO: useEffect séparé pour les changements de mode/enabled (sans volume)
  useEffect(() => {
    console.log(`🎵 AmbientSoundManagerV2: Mode=${currentSkyMode}, Enabled=${enabled} (Source: props INDÉPENDANT)`);

    // 🔧 CISCO: CORRECTION BOUCLE INFINIE - Protection renforcée pour le mode dawn
    const now = Date.now();
    const minDelay = currentSkyMode === 'dawn' ? 1000 : 500; // Plus de délai pour dawn
    if (now - lastModeChangeTime.current < minDelay) {
      console.log(`🔄 Changement de mode trop rapide (${currentSkyMode}), ignoré pour éviter boucle infinie`);
      return;
    }
    lastModeChangeTime.current = now;

    const newSoundConfig = SOUND_CONFIG[currentSkyMode];
    const newSoundKey = currentSkyMode;

    // --- Audio Enabled/Disabled Logic ---
    if (!enabled) {
      if (audioRefs.current.length > 0) {
        console.log(`🔇 Audio désactivé. Arrêt de ${audioRefs.current.length} sons du mode ${currentSoundKey.current}.`);

        // Arrêter tous les tweens
        fadeTweens.current.forEach(tween => tween?.kill());
        fadeTweens.current = [];

        // 🔧 CISCO: Arrêter tous les timeouts de répétition
        repeatTimeouts.current.forEach(timeout => clearTimeout(timeout));
        repeatTimeouts.current = [];

        // Fade out et arrêt de tous les audios
        audioRefs.current.forEach((audio, index) => {
          if (audio) {
            gsap.to(audio, {
              volume: 0,
              duration: 1,
              onComplete: () => {
                audio.pause();
                console.log(`🔇 Audio ${index + 1} arrêté.`);
              }
            });
          }
        });

        // Nettoyer les références
        audioRefs.current = [];
        currentSoundKey.current = null;
        currentSoundFiles.current = [];
        console.log("🔇 Tous les audios arrêtés et nettoyés.");
      }
      return; // Arrêter ici si l'audio est désactivé
    }

    // --- Sound Change Logic ---
    if (enabled && newSoundKey !== currentSoundKey.current) {
      console.log(`🎵 Changement de mode: ${currentSoundKey.current} → ${newSoundKey}`);

      // 1. Fade out et arrêt de TOUS les anciens sons
      if (audioRefs.current.length > 0) {
        console.log(`🎵 Arrêt progressif de ${audioRefs.current.length} sons du mode ${currentSoundKey.current}`);

        // 🔧 CISCO: Arrêter les tweens en cours proprement
        fadeTweens.current.forEach(tween => tween?.kill());
        fadeTweens.current = [];

        // Fade out de tous les audios
        audioRefs.current.forEach((audio, index) => {
          if (audio) {
            const fadeOutTween = gsap.to(audio, {
              volume: 0,
              duration: (newSoundConfig?.fadeOutDuration || 2000) / 1000,
              ease: "power1.inOut",
              overwrite: true, // 🔧 CISCO: Assurer que cette animation prend le dessus
              onComplete: () => {
                audio.pause();
                console.log(`🎵 Ancien son ${index + 1} (${currentSoundFiles.current[index]}) arrêté.`);
              }
            });
            // 🔧 CISCO: Ajouter le tween de fade out à la liste pour un nettoyage propre
            fadeTweens.current.push(fadeOutTween);
          }
        });
      }

      // 2. Démarrer TOUS les nouveaux sons si la configuration existe
      if (newSoundConfig && newSoundConfig.sounds.length > 0) {
        console.log(`🎵 Démarrage de ${newSoundConfig.sounds.length} sons du dossier ${newSoundConfig.folder}`);

        // Nettoyer les anciennes références
        audioRefs.current = [];
        fadeTweens.current = [];
        currentSoundKey.current = newSoundKey;
        currentSoundFiles.current = [...newSoundConfig.sounds];

        // Créer et jouer TOUS les fichiers du dossier
        newSoundConfig.sounds.forEach((soundFile, index) => {
          const soundUrl = getSoundUrl(soundFile, newSoundConfig.folder);
          console.log(`🎵 Chargement son ${index + 1}/${newSoundConfig.sounds.length}: ${soundFile}`);

          const newAudio = new Audio(soundUrl);

          // 🔧 CISCO: Gestion spéciale pour les sons courts avec temporisation
          if ((soundFile === 'insect_bee_fly.mp3' || soundFile === 'hibou-molkom.mp3') && newSoundConfig.repeatDelay) {
            newAudio.loop = false; // Pas de loop automatique pour les sons avec temporisation
            console.log(`🔄 Son avec temporisation détecté: ${soundFile} (délai: ${newSoundConfig.repeatDelay}ms)`);
          } else {
            newAudio.loop = true; // Loop normal pour les autres sons
          }

          newAudio.volume = 0; // Commencer silencieux pour le fade in
          audioRefs.current.push(newAudio);

          // Jouer puis faire le fade in
          playAudioWithContext(newAudio).then(() => {
            console.log(`🎵 Fade in du son ${index + 1}: ${soundFile}`);
            const targetVolume = enabled ? (newSoundConfig.volume * volume) : 0; // 🔧 CISCO: Respecter l'état enabled
            const fadeInTween = gsap.to(newAudio, {
              volume: targetVolume,
              duration: newSoundConfig.fadeInDuration / 1000 || 2.0,
              ease: "power1.inOut",
              overwrite: true // 🔧 CISCO: Assurer que cette animation prend le dessus
            });
            fadeTweens.current.push(fadeInTween);

            // 🔧 CISCO: Gestion spécialisée pour le hibou en mode nuit profonde
            if (soundFile === 'hibou-molkom.mp3' && newSoundConfig.repeatDelay) {
              const setupOwlRepeat = () => {
                // 🦉 CISCO: Délai variable entre 3 et 4 minutes pour naturalisme (demande Cisco)
                const randomDelay = 180000 + Math.random() * 60000; // Entre 180s (3min) et 240s (4min)

                const timeout = setTimeout(() => {
                  if (audioRefs.current.includes(newAudio) && enabled && currentSkyMode === 'night') {
                    newAudio.currentTime = 0;
                    playAudioWithContext(newAudio).then(() => {
                      console.log(`🦉 Hibou répété après ${Math.round(randomDelay/1000)}s (${Math.round(randomDelay/60000)} minutes)`);
                      setupOwlRepeat(); // Programmer la prochaine répétition avec nouveau délai aléatoire
                    }).catch(error => {
                      console.error(`❌ Erreur répétition hibou:`, error);
                    });
                  }
                }, randomDelay);
                repeatTimeouts.current.push(timeout);
              };

              // Programmer la première répétition après la fin du son initial
              newAudio.addEventListener('ended', setupOwlRepeat);
              console.log(`🦉 Temporisation hibou activée - répétition toutes les 3-4 minutes`);

            } else if (soundFile === 'insect_bee_fly.mp3' && newSoundConfig.repeatDelay) {
              // 🐝 CISCO: Gestion normale pour les autres sons courts
              const setupRepeat = () => {
                const timeout = setTimeout(() => {
                  if (audioRefs.current.includes(newAudio) && enabled) {
                    newAudio.currentTime = 0;
                    playAudioWithContext(newAudio).then(() => {
                      console.log(`🔄 Répétition du son: ${soundFile}`);
                      setupRepeat(); // Programmer la prochaine répétition
                    }).catch(error => {
                      console.error(`❌ Erreur répétition ${soundFile}:`, error);
                    });
                  }
                }, newSoundConfig.repeatDelay);
                repeatTimeouts.current.push(timeout);
              };

              // Programmer la première répétition après la fin du son
              newAudio.addEventListener('ended', setupRepeat);
            }
          }).catch(error => {
            // 🔧 CISCO: Filtrer les erreurs AbortError qui sont normales lors des changements de mode
            if (error.name === 'AbortError') {
              console.log(`🔄 Transition audio normale: ${soundFile} interrompu pour changement de mode`);
            } else {
              console.error(`❌ Erreur lors de la lecture du son ${soundUrl}:`, error);
            }
            // Retirer ce son défaillant de la liste
            const audioIndex = audioRefs.current.indexOf(newAudio);
            if (audioIndex > -1) {
              audioRefs.current.splice(audioIndex, 1);
            }
          });
        });
      } else {
        // Si le nouveau mode n'a pas de son, s'assurer qu'on est silencieux
        console.log(`🔇 Aucun son configuré pour le mode: ${newSoundKey}`);
        audioRefs.current = [];
        currentSoundKey.current = newSoundKey;
        currentSoundFiles.current = [];
      }
    }

    // --- Cleanup on unmount ---
    return () => {
      console.log("🧹 Nettoyage AmbientSoundManagerV2 au démontage.");

      // Arrêter tous les tweens
      fadeTweens.current.forEach(tween => tween?.kill());
      fadeTweens.current = [];

      // 🔧 CISCO: Arrêter tous les timeouts de répétition
      repeatTimeouts.current.forEach(timeout => clearTimeout(timeout));
      repeatTimeouts.current = [];

      // Arrêter et nettoyer tous les audios
      audioRefs.current.forEach((audio, index) => {
        if (audio) {
          audio.pause();
          console.log(`🧹 Audio ${index + 1} nettoyé.`);
        }
      });
      audioRefs.current = [];
      currentSoundFiles.current = [];
    };
  }, [currentSkyMode, enabled, playAudioWithContext]); // 🔧 CISCO: Dépendances INDÉPENDANTES - Plus de contexte

  // 🔧 CISCO: useEffect séparé UNIQUEMENT pour les changements de volume et enabled - CORRIGÉ
  useEffect(() => {
    // Si pas d'audio actif, ne rien faire
    if (audioRefs.current.length === 0) return;

    const currentSoundConfig = SOUND_CONFIG[currentSoundKey.current || ''];
    if (!currentSoundConfig) return;

    if (enabled) {
      // 🔊 CISCO: Audio activé - Reprendre la lecture et ajuster le volume
      const targetVolume = currentSoundConfig.volume * volume;
      console.log(`🔊 ACTIVATION audio: ${audioRefs.current.length} sons, volume: ${targetVolume.toFixed(2)}`);

      audioRefs.current.forEach((audio, index) => {
        if (audio) {
          // Reprendre la lecture si elle était en pause
          if (audio.paused) {
            playAudioWithContext(audio).catch(error => {
              console.error(`❌ Erreur reprise audio ${index}:`, error);
            });
          }

          // Ajuster le volume avec animation
          gsap.to(audio, {
            volume: targetVolume,
            duration: 0.3,
            ease: "power1.out",
            overwrite: true
          });
        }
      });
    } else {
      // 🔇 CISCO: Audio désactivé - Mettre en pause avec fade out
      console.log(`🔇 DÉSACTIVATION audio: ${audioRefs.current.length} sons`);

      audioRefs.current.forEach((audio, index) => {
        if (audio && !audio.paused) {
          // Fade out puis pause
          gsap.to(audio, {
            volume: 0,
            duration: 0.3,
            ease: "power1.out",
            overwrite: true,
            onComplete: () => {
              audio.pause();
              console.log(`⏸️ Audio ${index + 1} mis en pause`);
            }
          });
        }
      });
    }
  }, [volume, enabled, playAudioWithContext]); // Ajout de playAudioWithContext dans les dépendances

  return null; // Ce composant ne rend rien visuellement
};

export default AmbientSoundManagerV2;
