
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**
# ContextEngineering\Tasks\Cisco.md
# ContextEngineering\TechnicalNotes\journal-technique.md


# Avec mon approbation écrite, tu peux commencer à travailler sur la tâche.


- 


- 


- 


- Attention, rappel, certains dossiers ne doivent pas être commités sur GitHub, comme le context engineering et certains fichiers sensibles, comme par exemple s'il y a des clés, attention, il ne faut pas qu'elles soient visibles. 


- Ensuite, il faudra peut-être penser à repositionner le soleil quand il se lève, le faire monter au zénith tout en haut à droite de l'écran, et ensuite à l'inverse, quand on arrive à la fin de la journée, le soleil doit redescendre puis se coucher. Ensuite, quand la nuit vient, c'est la lune qui prend le relais. Donc elle se lève, elle monte au-dessus du paysage, background. Ensuite, pareil, elle monte, elle part de la gauche, elle monte au zénith, et ensuite, quand le cycle est fait et que le lever de soleil vient à son tour, la lune se couche et le soleil prend le relais. Pensez bien à mettre à jour le fichier MD complet pour intervention manuelle pour le soleil et la lune pour que je puisse intervenir manuellement. 



# Gros problème avec les sons, ils ne sont pas synchronisés correctement. 
- Chaque mode doit déclencher un son particulier. Quand le mode est fini, le son de ce même mode doit s'atténuer ou s'interrompre. Le meilleur serait qu'il s'atténue progressivement, puis les autres prennent le relais. À chaque fois qu'un mode se déclenche à la suite, les autres sons successifs doivent arriver progressivement dans chaque mode respectif. Je vous donne un exemple. Le merle, on l'entend sur tous les modes. Et normalement, le merle, on l'entend au lever du soleil, enfin entre l'aube et le lever du soleil, parce que je ne sais plus ce qu'on a enlevé exactement. Et normalement, au coucher du soleil, on l'entend. Mais la nuit, on ne l'entend pas. Normalement, on ne devrait pas l'entendre. Et pourtant, on l'entend dans tous les modes. Donc veuillez vérifier encore une fois tous les modes audio, parce que je vous garantis qu'il est désynchronisé. 

- Voici ici dessous le répertoire avec les bons fichiers qui doit être synchronisé avec chaque mode. Chaque mode doit être respectif de son fond sonore. Il ne faut pas tout mélanger. Donc chaque fond sonore est approprié pour chaque mode. Je répète, pour chaque mode. Dès qu'un mode passe, il doit s'atténuer ou s'interrompre, puis les autres fonds sonores doivent prendre le relais. 
public\sounds\coucher-soleil
public\sounds\coucher-soleil\bird-chirp.mp3
public\sounds\coucher-soleil\grillon-drome.mp3
public\sounds\lever-soleil
public\sounds\lever-soleil\blackbird.mp3
public\sounds\lever-soleil\Lever_soleil-nature.mp3
public\sounds\matin\insect_bee_fly.mp3
public\sounds\matin\morning-birdsong.mp3
public\sounds\midi
public\sounds\midi\campagne-birds.mp3
public\sounds\midi\forest_cicada.mp3
public\sounds\nuit-profonde\hibou-molkom.mp3
public\sounds\nuit-profonde\night-atmosphere-with-crickets-374652.mp3
public\sounds\nuit-profonde
public\sounds\nuit-profonde\sounds-crickets-nuit_profonde.mp3



- Attention, je viens de remarquer à la fin du mode aube et quand on passe au mode midi, il y a un gros problème de dégradé, il y a quelque chose qui ne va pas. On a une couleur bizarroïde au début du mode midi, on a un sursaut de couleur orange et ce n'est pas normal du tout, il faut corriger. 


- Il y a la position du soleil à corriger quand on est au mode midi. Il est bien au zénith en haut dans l'angle à droite de l'écran, pas de problème. Mais par contre quand on passe au mode suivant, le soleil devrait normalement descendre au fur et à mesure pour atteindre le mode coucher de soleil. Mais là ce n'est pas le cas, le soleil est bloqué en haut. Et d'ailleurs, pire, je viens de voir en mode couché de soleil, il fait l'inverse, il monte au lieu de descendre, c'est même pire. Lol, il va falloir corriger. 
N'hésitez pas à faire apparaître quelques étoiles progressivement en mode couché de soleil. C'est très important. Et surtout, il y a une correction au niveau dégradé. Le ciel en haut de l'écran doit s'assombrir, puis la couleur doit s'estomper au niveau du rouge-orange en bas de l'écran. Puisque de toute façon, nous allons rejoindre le mode nuit, donc tout faut que ce soit cohérent. 


-














































































